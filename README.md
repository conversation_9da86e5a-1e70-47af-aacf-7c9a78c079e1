# 🚀 Traefik Controller for 10Baht Projects

A comprehensive Traefik v3.0 setup for managing multiple Docker projects with HTTP-only configuration and automatic service discovery.

## ✨ Features

- **HTTP-Only by Default**: Simple setup without SSL complexity
- **Automatic Service Discovery**: Projects register themselves with Traefik
- **Docker Localhost Domains**: Use `docker.localhost` domains that work automatically
- **Port Management**: No more port conflicts - <PERSON><PERSON><PERSON><PERSON> handles everything on port 80
- **Dashboard**: Web interface for monitoring routes and services
- **Project Management**: Scripts to start/stop individual projects
- **Label Generation**: Automatic Traefik label generation for projects
- **Optional TLS**: Enable HTTPS per-project when needed

## 🚀 Quick Start

```bash
# 1. Start Traefik Controller
cd traefik_controller
./traefik-ctl start

# 2. Generate labels for your project
./traefik-ctl add-labels myproject api 8000

# 3. Add the generated labels to your project's docker-compose.yml
# 4. Start your project and access it at http://myproject.docker.localhost
```

## 📊 Dashboard Access

- **Domain**: http://traefik.docker.localhost
- **Direct**: http://localhost:8080/dashboard/
- **API**: http://localhost:8080/api/

## 🔧 How It Works

1. **Traefik Controller**: Runs as the main reverse proxy on port 80
2. **Project Self-Registration**: Projects add Traefik labels to their services
3. **Automatic Discovery**: Traefik automatically detects labeled containers
4. **Domain Resolution**: Projects become accessible via `docker.localhost` domains
5. **HTTP-Only**: Simple HTTP routing without SSL complexity

## �� Adding Traefik to Your Project

### Method 1: Use the Label Generator (Recommended)

```bash
# Generate HTTP-only labels (default)
./traefik-ctl add-labels myproject api 8000

# Generate TLS-enabled labels (optional)
./traefik-ctl add-labels myproject api 8000 --tls
```

### Method 2: Manual Configuration

Add these labels to your service in `.dockerwrapper/docker-compose.yml`:

**HTTP-Only (Default):**
```yaml
services:
  your-app:
    # ... your existing configuration ...
    labels:
      - traefik.enable=true
      - traefik.http.routers.myproject-api.rule=Host("myproject.docker.localhost")
    networks:
      - net

networks:
  net:
    external: true
    name: net
```

**With TLS (Optional):**
```yaml
services:
  your-app:
    # ... your existing configuration ...
    labels:
      - traefik.enable=true
      - traefik.http.routers.myproject-api.tls=true
      - traefik.http.routers.myproject-api.rule=Host("myproject.docker.localhost")
    networks:
      - net
```

## 🌐 Example Project Domains

- `whoami.docker.localhost` - Test whoami service
- `turdparty.docker.localhost` - TurdParty project
- `certrats.docker.localhost` - CertRats project
- `webotter.docker.localhost` - WebOTteR project
- `traefik.docker.localhost` - Traefik dashboard

## 🛠️ Management Commands

```bash
# Start/stop Traefik
./traefik-ctl start
./traefik-ctl stop

# Check status and routes
./traefik-ctl status
./traefik-ctl routes

# Generate labels for your project
./traefik-ctl add-labels myproject api 8000
./traefik-ctl add-labels myproject api 8000 --tls  # With TLS

# View dashboard
./traefik-ctl dashboard
```

## 🔍 Troubleshooting

- **Domain not resolving**: `docker.localhost` should work automatically
- **Port conflicts**: Stop other services using port 80
- **Service not appearing**: Check if container has correct Traefik labels and is on `net` network
- **Container not starting**: Check logs with `docker compose logs traefik`

## 📝 Notes

- **Default Mode**: HTTP-only for simplicity
- **Network**: All services use the `net` network
- **Ports**: Only port 80 (HTTP) and 8080 (dashboard) are exposed
- **Domains**: `docker.localhost` domains work automatically without DNS configuration
- **TLS**: Available per-project with `--tls` flag when needed
