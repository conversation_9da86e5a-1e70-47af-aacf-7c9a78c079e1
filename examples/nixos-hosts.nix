# Add this to your NixOS configuration.nix to enable .10baht domains

{
  # Add .10baht domains to /etc/hosts
  networking.extraHosts = ''
    127.0.0.1 traefik.10baht
    127.0.0.1 turdparty.10baht
    127.0.0.1 certrats.10baht
    127.0.0.1 webotter.10baht
    127.0.0.1 inspector-gadget.10baht
    127.0.0.1 regression-rigor.10baht
    127.0.0.1 hooker-pill.10baht
    127.0.0.1 test-project.10baht
  '';

  # Alternative: Use dnsmasq for .10baht domains
  services.dnsmasq = {
    enable = true;
    settings = {
      address = "/.10baht/127.0.0.1";
      listen-address = "127.0.0.1";
      port = 5353;
    };
  };
}

# After adding this, run:
# sudo nixos-rebuild switch
