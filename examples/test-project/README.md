# Test Project Example

This is a simple test project to demonstrate Traefik self-registration.

## How to Test

1. Make sure Traefik Controller is running:
   ```bash
   cd ../../
   ./traefik-ctl status
   ```

2. Start this test project:
   ```bash
   docker compose up -d
   ```

3. Visit https://test-project.10baht in your browser

4. Check that the route appears in Traefik:
   ```bash
   cd ../../
   ./traefik-ctl routes
   ```

5. Stop the test project:
   ```bash
   docker compose down
   ```

## What This Demonstrates

- **Self-Registration**: The project automatically registers with Traefik when started
- **SSL Termination**: Traefik handles HTTPS with locally trusted certificates  
- **Domain Routing**: Accessible via test-project.10baht domain
- **No Port Conflicts**: No need to expose ports externally

## Key Configuration

The important parts of the docker-compose.yml:

```yaml
networks:
  - traefik-network  # Connect to Traefik

labels:
  - "traefik.enable=true"
  - "traefik.http.routers.test-project.rule=Host(`test-project.10baht`)"
  - "traefik.http.routers.test-project.entrypoints=websecure"
  - "traefik.http.routers.test-project.tls=true"
  - "traefik.http.services.test-project.loadbalancer.server.port=80"
```

This is exactly what you'd add to your own projects!
