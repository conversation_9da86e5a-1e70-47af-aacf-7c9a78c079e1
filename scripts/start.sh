#!/bin/bash

# Start Traefik Controller
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAEFIK_DIR="$(dirname "$SCRIPT_DIR")"

echo "🚀 Starting Traefik Controller..."

# Change to traefik directory
cd "$TRAEFIK_DIR"

# Create required directories
mkdir -p logs certs dynamic projects

# Check if certificates exist
if [ ! -f "certs/10baht.crt" ] || [ ! -f "certs/10baht.key" ]; then
    echo "⚠️  SSL certificates not found!"
    echo "🔧 Run './scripts/install-mkcert.sh' first to generate certificates"
    exit 1
fi

# Create dynamic TLS configuration
cat > dynamic/tls.yml << EOF
tls:
  certificates:
    - certFile: /etc/traefik/certs/10baht.crt
      keyFile: /etc/traefik/certs/10baht.key
      stores:
        - default
  stores:
    default:
      defaultCertificate:
        certFile: /etc/traefik/certs/10baht.crt
        keyFile: /etc/traefik/certs/10baht.key
EOF

echo "📋 Starting Traefik container..."
docker compose up -d

# Wait for Traefik to start
echo "⏳ Waiting for Traefik to start..."
sleep 5

# Check if Traefik is running
if docker compose ps | grep -q "traefik-controller.*Up"; then
    echo "✅ Traefik Controller started successfully!"
    echo ""
    echo "🌐 Traefik Dashboard: http://traefik.docker.localhost"
    echo "📊 Metrics endpoint: http://localhost:8080/metrics"
    echo ""
    echo "📝 Next steps:"
    echo "   1. Run './scripts/discover-projects.sh' to configure your projects"
    echo "   2. Start projects with './scripts/start-project.sh <project-name>'"
    echo ""
    echo "🔍 View logs with: docker compose logs -f traefik"
else
    echo "❌ Failed to start Traefik Controller"
    echo "🔍 Check logs with: docker compose logs traefik"
    exit 1
fi
