# Traefik Static Configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# Entry points
entryPoints:
  web:
    address: ":80"

# Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: net
    watch: true
  
  file:
    directory: /etc/traefik/dynamic
    watch: true

# API and Dashboard
api:
  dashboard: true
  insecure: true
  debug: false

# Logging
log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"

accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json

# Metrics
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true


